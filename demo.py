import pandas as pd
import openpyxl
from googletrans import Translator
import time
import os
from typing import Dict, List, Optional
import logging

class ExcelTranslator:
    def __init__(self, source_lang='en', target_lang='ja', delay=0.1):
        """
        Initialize the Excel translator
        
        Args:
            source_lang (str): Source language code (default: 'en' for English)
            target_lang (str): Target language code (default: 'ja' for Japanese)
            delay (float): Delay between API calls to avoid rate limiting
        """
        self.translator = Translator()
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.delay = delay
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def translate_text(self, text: str) -> str:
        """
        Translate a single text string
        
        Args:
            text (str): Text to translate
            
        Returns:
            str: Translated text
        """
        if pd.isna(text) or text == "" or not isinstance(text, str):
            return text
        
        try:
            # Add delay to avoid rate limiting
            time.sleep(self.delay)
            
            result = self.translator.translate(
                text, 
                src=self.source_lang, 
                dest=self.target_lang
            )
            return result.text
        except Exception as e:
            self.logger.warning(f"Translation failed for '{text}': {e}")
            return text  # Return original text if translation fails
    
    def translate_dataframe(self, df: pd.DataFrame, translate_headers: bool = True) -> pd.DataFrame:
        """
        Translate all text content in a DataFrame
        
        Args:
            df (pd.DataFrame): DataFrame to translate
            translate_headers (bool): Whether to translate column headers
            
        Returns:
            pd.DataFrame: Translated DataFrame
        """
        df_translated = df.copy()
        
        # Translate headers if requested
        if translate_headers:
            self.logger.info("Translating column headers...")
            new_columns = []
            for col in df.columns:
                if isinstance(col, str):
                    translated_col = self.translate_text(col)
                    new_columns.append(translated_col)
                    self.logger.info(f"Header: '{col}' -> '{translated_col}'")
                else:
                    new_columns.append(col)
            df_translated.columns = new_columns
        
        # Translate cell content
        self.logger.info("Translating cell content...")
        total_cells = 0
        translated_cells = 0
        
        for col in df_translated.columns:
            for idx in df_translated.index:
                cell_value = df_translated.at[idx, col]
                if isinstance(cell_value, str) and cell_value.strip():
                    total_cells += 1
                    translated_value = self.translate_text(cell_value)
                    df_translated.at[idx, col] = translated_value
                    translated_cells += 1
                    
                    if translated_cells % 10 == 0:  # Progress update every 10 translations
                        self.logger.info(f"Translated {translated_cells}/{total_cells} cells...")
        
        self.logger.info(f"Translation complete: {translated_cells}/{total_cells} cells translated")
        return df_translated
    
    def translate_excel_file(self, 
                           input_file: str, 
                           output_file: str, 
                           sheet_names: Optional[List[str]] = None,
                           translate_headers: bool = True) -> None:
        """
        Translate an entire Excel file with multiple sheets
        
        Args:
            input_file (str): Path to input Excel file
            output_file (str): Path to output Excel file
            sheet_names (List[str], optional): List of sheet names to translate. 
                                             If None, translates all sheets.
            translate_headers (bool): Whether to translate column headers
        """
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Input file '{input_file}' not found")
        
        self.logger.info(f"Starting translation of '{input_file}'")
        self.logger.info(f"Source language: {self.source_lang}, Target language: {self.target_lang}")
        
        # Read all sheets from the Excel file
        try:
            excel_file = pd.ExcelFile(input_file)
            all_sheet_names = excel_file.sheet_names
            
            # Determine which sheets to translate
            if sheet_names is None:
                sheets_to_translate = all_sheet_names
            else:
                sheets_to_translate = [name for name in sheet_names if name in all_sheet_names]
                missing_sheets = [name for name in sheet_names if name not in all_sheet_names]
                if missing_sheets:
                    self.logger.warning(f"Sheets not found: {missing_sheets}")
            
            self.logger.info(f"Sheets to translate: {sheets_to_translate}")
            
            # Create a dictionary to store translated sheets
            translated_sheets = {}
            
            # Translate each sheet
            for sheet_name in sheets_to_translate:
                self.logger.info(f"\n--- Translating sheet: '{sheet_name}' ---")
                
                # Read the sheet
                df = pd.read_excel(input_file, sheet_name=sheet_name)
                self.logger.info(f"Sheet '{sheet_name}' loaded: {df.shape[0]} rows, {df.shape[1]} columns")
                
                # Translate the sheet
                df_translated = self.translate_dataframe(df, translate_headers)
                translated_sheets[sheet_name] = df_translated
                
                self.logger.info(f"Sheet '{sheet_name}' translation complete")
            
            # Write all translated sheets to the output file
            self.logger.info(f"\nWriting translated sheets to '{output_file}'...")
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for sheet_name, df_translated in translated_sheets.items():
                    df_translated.to_excel(writer, sheet_name=sheet_name, index=False)
                    self.logger.info(f"Sheet '{sheet_name}' written to output file")
            
            self.logger.info(f"\nTranslation complete! Output saved to: {output_file}")
            
        except Exception as e:
            self.logger.error(f"Error during translation: {e}")
            raise
    
    def get_translation_preview(self, input_file: str, sheet_name: str = None, num_rows: int = 5) -> None:
        """
        Show a preview of translation for the first few rows
        
        Args:
            input_file (str): Path to input Excel file
            sheet_name (str, optional): Sheet name to preview. If None, uses first sheet.
            num_rows (int): Number of rows to preview
        """
        if sheet_name is None:
            excel_file = pd.ExcelFile(input_file)
            sheet_name = excel_file.sheet_names[0]
        
        df = pd.read_excel(input_file, sheet_name=sheet_name)
        df_preview = df.head(num_rows)
        
        print(f"\n--- Preview of '{sheet_name}' (First {num_rows} rows) ---")
        print("ORIGINAL:")
        print(df_preview.to_string())
        
        print(f"\nTRANSLATED PREVIEW:")
        df_translated_preview = self.translate_dataframe(df_preview, translate_headers=True)
        print(df_translated_preview.to_string())


def main():
    """
    Example usage of the ExcelTranslator
    """
    # Initialize translator (English to Japanese)
    translator = ExcelTranslator(source_lang='en', target_lang='ja', delay=0.1)
    
    # Example 1: Translate entire Excel file
    input_file = "input_data.xlsx"
    output_file = "translated_data_jp.xlsx"
    
    try:
        # Option 1: Translate all sheets
        translator.translate_excel_file(
            input_file=input_file,
            output_file=output_file,
            translate_headers=True  # Translate column headers
        )
        
        # Option 2: Translate specific sheets only
        # translator.translate_excel_file(
        #     input_file=input_file,
        #     output_file=output_file,
        #     sheet_names=['Sheet1', 'Data', 'Summary'],  # Specify sheet names
        #     translate_headers=True
        # )
        
        # Option 3: Preview translation before processing entire file
        # translator.get_translation_preview(input_file, num_rows=3)
        
    except FileNotFoundError:
        print(f"Please ensure '{input_file}' exists in the current directory")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()


# Additional utility functions

def batch_translate_files(file_list: List[str], output_dir: str = "translated"):
    """
    Translate multiple Excel files in batch
    
    Args:
        file_list (List[str]): List of input file paths
        output_dir (str): Directory to save translated files
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    translator = ExcelTranslator(source_lang='en', target_lang='ja')
    
    for input_file in file_list:
        if os.path.exists(input_file):
            filename = os.path.basename(input_file)
            name, ext = os.path.splitext(filename)
            output_file = os.path.join(output_dir, f"{name}_translated{ext}")
            
            print(f"\nProcessing: {input_file}")
            translator.translate_excel_file(input_file, output_file)
        else:
            print(f"File not found: {input_file}")

def create_sample_excel():
    """
    Create a sample Excel file for testing
    """
    # Sample data
    data1 = {
        'Name': ['John Smith', 'Jane Doe', 'Bob Johnson'],
        'Department': ['Engineering', 'Marketing', 'Sales'],
        'Position': ['Software Engineer', 'Marketing Manager', 'Sales Representative'],
        'Location': ['New York', 'Los Angeles', 'Chicago']
    }
    
    data2 = {
        'Product': ['Laptop', 'Mouse', 'Keyboard'],
        'Category': ['Electronics', 'Accessories', 'Accessories'],
        'Price': [999.99, 29.99, 79.99],
        'Description': ['High-performance laptop', 'Wireless optical mouse', 'Mechanical keyboard']
    }
    
    # Create Excel file with multiple sheets
    with pd.ExcelWriter('sample_data.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(data1).to_excel(writer, sheet_name='Employees', index=False)
        pd.DataFrame(data2).to_excel(writer, sheet_name='Products', index=False)
    
    print("Sample Excel file 'sample_data.xlsx' created with 'Employees' and 'Products' sheets")

# Uncomment the line below to create a sample file for testing
# create_sample_excel()