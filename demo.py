import pandas as pd
import openpyxl
from openpyxl import load_workbook, Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment, Protection, NamedStyle
from openpyxl.formatting.rule import Rule
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.drawing.image import Image as OpenpyxlImage
from openpyxl.chart import *
from openpyxl.utils import get_column_letter
from googletrans import Translator
import time
import os
from typing import Dict, List, Optional
import logging
import copy
import shutil

class ExcelTranslator:
    def __init__(self, source_lang='en', target_lang='ja', delay=0.1):
        """
        Initialize the Excel translator

        Args:
            source_lang (str): Source language code (default: 'en' for English)
            target_lang (str): Target language code (default: 'ja' for Japanese)
            delay (float): Delay between API calls to avoid rate limiting
        """
        self.translator = Translator()
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.delay = delay

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def copy_cell_style(self, source_cell, target_cell):
        """
        Copy all formatting from source cell to target cell
        """
        if source_cell.has_style:
            target_cell.font = copy.copy(source_cell.font)
            target_cell.border = copy.copy(source_cell.border)
            target_cell.fill = copy.copy(source_cell.fill)
            target_cell.number_format = source_cell.number_format
            target_cell.protection = copy.copy(source_cell.protection)
            target_cell.alignment = copy.copy(source_cell.alignment)

    def copy_worksheet_properties(self, source_ws, target_ws):
        """
        Copy worksheet-level properties and formatting
        """
        # Copy sheet properties
        target_ws.sheet_format = copy.copy(source_ws.sheet_format)
        target_ws.sheet_properties = copy.copy(source_ws.sheet_properties)
        target_ws.page_setup = copy.copy(source_ws.page_setup)
        target_ws.print_options = copy.copy(source_ws.print_options)
        target_ws.page_margins = copy.copy(source_ws.page_margins)

        # Copy column dimensions
        for col_letter, col_dim in source_ws.column_dimensions.items():
            target_ws.column_dimensions[col_letter] = copy.copy(col_dim)

        # Copy row dimensions
        for row_num, row_dim in source_ws.row_dimensions.items():
            target_ws.row_dimensions[row_num] = copy.copy(row_dim)

        # Copy merged cells
        for merged_range in source_ws.merged_cells.ranges:
            target_ws.merge_cells(str(merged_range))

        # Copy conditional formatting
        for cf in source_ws.conditional_formatting:
            target_ws.conditional_formatting.add(cf.coord, copy.copy(cf.cfRule[0]))

        # Copy data validations
        for dv in source_ws.data_validations.dataValidation:
            target_ws.add_data_validation(copy.copy(dv))

    def translate_excel_with_formatting(self,
                                      input_file: str,
                                      output_file: str,
                                      sheet_names: Optional[List[str]] = None,
                                      translate_headers: bool = True) -> None:
        """
        Translate Excel file while preserving ALL formatting, styles, colors, themes, fonts, and metadata

        Args:
            input_file (str): Path to input Excel file
            output_file (str): Path to output Excel file
            sheet_names (List[str], optional): List of sheet names to translate.
                                             If None, translates all sheets.
            translate_headers (bool): Whether to translate column headers
        """
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Input file '{input_file}' not found")

        self.logger.info(f"Starting translation with formatting preservation of '{input_file}'")
        self.logger.info(f"Source language: {self.source_lang}, Target language: {self.target_lang}")

        try:
            # Load the workbook with all formatting
            source_wb = load_workbook(input_file, data_only=False, keep_vba=True)

            # Create a copy of the workbook to preserve all metadata and properties
            shutil.copy2(input_file, output_file)
            target_wb = load_workbook(output_file, data_only=False, keep_vba=True)

            # Determine which sheets to translate
            all_sheet_names = source_wb.sheetnames
            if sheet_names is None:
                sheets_to_translate = all_sheet_names
            else:
                sheets_to_translate = [name for name in sheet_names if name in all_sheet_names]
                missing_sheets = [name for name in sheet_names if name not in all_sheet_names]
                if missing_sheets:
                    self.logger.warning(f"Sheets not found: {missing_sheets}")

            self.logger.info(f"Sheets to translate: {sheets_to_translate}")

            # Translate each sheet while preserving formatting
            for sheet_name in sheets_to_translate:
                self.logger.info(f"\n--- Translating sheet: '{sheet_name}' ---")

                source_ws = source_wb[sheet_name]
                target_ws = target_wb[sheet_name]

                # Get the used range
                max_row = source_ws.max_row
                max_col = source_ws.max_column

                self.logger.info(f"Sheet '{sheet_name}' size: {max_row} rows, {max_col} columns")

                translated_cells = 0
                total_text_cells = 0

                # Process each cell
                for row in range(1, max_row + 1):
                    for col in range(1, max_col + 1):
                        source_cell = source_ws.cell(row=row, column=col)
                        target_cell = target_ws.cell(row=row, column=col)

                        # Copy the original value first
                        target_cell.value = source_cell.value

                        # Copy all formatting
                        self.copy_cell_style(source_cell, target_cell)

                        # Translate text content if it's a string
                        if isinstance(source_cell.value, str) and source_cell.value.strip():
                            total_text_cells += 1

                            # Skip translation for headers if not requested
                            if not translate_headers and row == 1:
                                continue

                            # Translate the text
                            translated_text = self.translate_text(source_cell.value)
                            target_cell.value = translated_text
                            translated_cells += 1

                            if translated_cells % 10 == 0:
                                self.logger.info(f"Translated {translated_cells}/{total_text_cells} cells...")

                # Copy worksheet-level properties
                self.copy_worksheet_properties(source_ws, target_ws)

                self.logger.info(f"Sheet '{sheet_name}' translation complete: {translated_cells}/{total_text_cells} cells translated")

            # Save the translated workbook
            target_wb.save(output_file)
            target_wb.close()
            source_wb.close()

            self.logger.info(f"\nTranslation complete! Output saved to: {output_file}")
            self.logger.info("✅ All formatting, styles, colors, themes, fonts, and metadata preserved!")

        except Exception as e:
            self.logger.error(f"Error during translation: {e}")
            raise
    
    def translate_text(self, text: str) -> str:
        """
        Translate a single text string
        
        Args:
            text (str): Text to translate
            
        Returns:
            str: Translated text
        """
        if pd.isna(text) or text == "" or not isinstance(text, str):
            return text
        
        try:
            # Add delay to avoid rate limiting
            time.sleep(self.delay)
            
            result = self.translator.translate(
                text, 
                src=self.source_lang, 
                dest=self.target_lang
            )
            return result.text
        except Exception as e:
            self.logger.warning(f"Translation failed for '{text}': {e}")
            return text  # Return original text if translation fails
    
    def translate_dataframe(self, df: pd.DataFrame, translate_headers: bool = True) -> pd.DataFrame:
        """
        Translate all text content in a DataFrame
        
        Args:
            df (pd.DataFrame): DataFrame to translate
            translate_headers (bool): Whether to translate column headers
            
        Returns:
            pd.DataFrame: Translated DataFrame
        """
        df_translated = df.copy()
        
        # Translate headers if requested
        if translate_headers:
            self.logger.info("Translating column headers...")
            new_columns = []
            for col in df.columns:
                if isinstance(col, str):
                    translated_col = self.translate_text(col)
                    new_columns.append(translated_col)
                    self.logger.info(f"Header: '{col}' -> '{translated_col}'")
                else:
                    new_columns.append(col)
            df_translated.columns = new_columns
        
        # Translate cell content
        self.logger.info("Translating cell content...")
        total_cells = 0
        translated_cells = 0
        
        for col in df_translated.columns:
            for idx in df_translated.index:
                cell_value = df_translated.at[idx, col]
                if isinstance(cell_value, str) and cell_value.strip():
                    total_cells += 1
                    translated_value = self.translate_text(cell_value)
                    df_translated.at[idx, col] = translated_value
                    translated_cells += 1
                    
                    if translated_cells % 10 == 0:  # Progress update every 10 translations
                        self.logger.info(f"Translated {translated_cells}/{total_cells} cells...")
        
        self.logger.info(f"Translation complete: {translated_cells}/{total_cells} cells translated")
        return df_translated
    
    def translate_excel_file(self, 
                           input_file: str, 
                           output_file: str, 
                           sheet_names: Optional[List[str]] = None,
                           translate_headers: bool = True) -> None:
        """
        Translate an entire Excel file with multiple sheets
        
        Args:
            input_file (str): Path to input Excel file
            output_file (str): Path to output Excel file
            sheet_names (List[str], optional): List of sheet names to translate. 
                                             If None, translates all sheets.
            translate_headers (bool): Whether to translate column headers
        """
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Input file '{input_file}' not found")
        
        self.logger.info(f"Starting translation of '{input_file}'")
        self.logger.info(f"Source language: {self.source_lang}, Target language: {self.target_lang}")
        
        # Read all sheets from the Excel file
        try:
            excel_file = pd.ExcelFile(input_file)
            all_sheet_names = excel_file.sheet_names
            
            # Determine which sheets to translate
            if sheet_names is None:
                sheets_to_translate = all_sheet_names
            else:
                sheets_to_translate = [name for name in sheet_names if name in all_sheet_names]
                missing_sheets = [name for name in sheet_names if name not in all_sheet_names]
                if missing_sheets:
                    self.logger.warning(f"Sheets not found: {missing_sheets}")
            
            self.logger.info(f"Sheets to translate: {sheets_to_translate}")
            
            # Create a dictionary to store translated sheets
            translated_sheets = {}
            
            # Translate each sheet
            for sheet_name in sheets_to_translate:
                self.logger.info(f"\n--- Translating sheet: '{sheet_name}' ---")
                
                # Read the sheet
                df = pd.read_excel(input_file, sheet_name=sheet_name)
                self.logger.info(f"Sheet '{sheet_name}' loaded: {df.shape[0]} rows, {df.shape[1]} columns")
                
                # Translate the sheet
                df_translated = self.translate_dataframe(df, translate_headers)
                translated_sheets[sheet_name] = df_translated
                
                self.logger.info(f"Sheet '{sheet_name}' translation complete")
            
            # Write all translated sheets to the output file
            self.logger.info(f"\nWriting translated sheets to '{output_file}'...")
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for sheet_name, df_translated in translated_sheets.items():
                    df_translated.to_excel(writer, sheet_name=sheet_name, index=False)
                    self.logger.info(f"Sheet '{sheet_name}' written to output file")
            
            self.logger.info(f"\nTranslation complete! Output saved to: {output_file}")
            
        except Exception as e:
            self.logger.error(f"Error during translation: {e}")
            raise
    
    def get_translation_preview(self, input_file: str, sheet_name: str = None, num_rows: int = 5) -> None:
        """
        Show a preview of translation for the first few rows
        
        Args:
            input_file (str): Path to input Excel file
            sheet_name (str, optional): Sheet name to preview. If None, uses first sheet.
            num_rows (int): Number of rows to preview
        """
        if sheet_name is None:
            excel_file = pd.ExcelFile(input_file)
            sheet_name = excel_file.sheet_names[0]
        
        df = pd.read_excel(input_file, sheet_name=sheet_name)
        df_preview = df.head(num_rows)
        
        print(f"\n--- Preview of '{sheet_name}' (First {num_rows} rows) ---")
        print("ORIGINAL:")
        print(df_preview.to_string())
        
        print(f"\nTRANSLATED PREVIEW:")
        df_translated_preview = self.translate_dataframe(df_preview, translate_headers=True)
        print(df_translated_preview.to_string())


def main():
    """
    Example usage of the ExcelTranslator with formatting preservation
    """
    # Initialize translator (English to Japanese)
    translator = ExcelTranslator(source_lang='en', target_lang='ja', delay=0.1)

    # Use demo.xlsx as input file
    input_file = "demo.xlsx"
    output_file = "demo_translated_formatted.xlsx"

    try:
        # NEW: Translate with full formatting preservation
        translator.translate_excel_with_formatting(
            input_file=input_file,
            output_file=output_file,
            translate_headers=True  # Translate column headers
        )

        print(f"\n🎉 SUCCESS! Translated file saved as: {output_file}")
        print("✅ All formatting, styles, colors, themes, fonts, and metadata preserved!")

        # Option 2: Translate specific sheets only (with formatting)
        # translator.translate_excel_with_formatting(
        #     input_file=input_file,
        #     output_file=output_file,
        #     sheet_names=['Sheet 1', 'Sheet 2'],  # Specify sheet names
        #     translate_headers=True
        # )

        # Option 3: Use old method (loses formatting)
        # translator.translate_excel_file(
        #     input_file=input_file,
        #     output_file="demo_translated_no_formatting.xlsx",
        #     translate_headers=True
        # )

    except FileNotFoundError:
        print(f"Please ensure '{input_file}' exists in the current directory")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()


# Additional utility functions

def batch_translate_files(file_list: List[str], output_dir: str = "translated"):
    """
    Translate multiple Excel files in batch
    
    Args:
        file_list (List[str]): List of input file paths
        output_dir (str): Directory to save translated files
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    translator = ExcelTranslator(source_lang='en', target_lang='ja')
    
    for input_file in file_list:
        if os.path.exists(input_file):
            filename = os.path.basename(input_file)
            name, ext = os.path.splitext(filename)
            output_file = os.path.join(output_dir, f"{name}_translated{ext}")
            
            print(f"\nProcessing: {input_file}")
            translator.translate_excel_file(input_file, output_file)
        else:
            print(f"File not found: {input_file}")

def create_sample_excel():
    """
    Create a sample Excel file for testing
    """
    # Sample data
    data1 = {
        'Name': ['John Smith', 'Jane Doe', 'Bob Johnson'],
        'Department': ['Engineering', 'Marketing', 'Sales'],
        'Position': ['Software Engineer', 'Marketing Manager', 'Sales Representative'],
        'Location': ['New York', 'Los Angeles', 'Chicago']
    }

    data2 = {
        'Product': ['Laptop', 'Mouse', 'Keyboard'],
        'Category': ['Electronics', 'Accessories', 'Accessories'],
        'Price': [999.99, 29.99, 79.99],
        'Description': ['High-performance laptop', 'Wireless optical mouse', 'Mechanical keyboard']
    }

    # Create Excel file with multiple sheets
    with pd.ExcelWriter('sample_data.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(data1).to_excel(writer, sheet_name='Employees', index=False)
        pd.DataFrame(data2).to_excel(writer, sheet_name='Products', index=False)

    print("Sample Excel file 'sample_data.xlsx' created with 'Employees' and 'Products' sheets")

def create_formatted_sample_excel():
    """
    Create a richly formatted sample Excel file for testing formatting preservation
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "Formatted Sample"

    # Add sample data with rich formatting
    headers = ['Product Name', 'Category', 'Price', 'Description', 'Status']
    data = [
        ['Premium Laptop', 'Electronics', 1299.99, 'High-performance gaming laptop', 'In Stock'],
        ['Wireless Mouse', 'Accessories', 49.99, 'Ergonomic wireless optical mouse', 'Low Stock'],
        ['Mechanical Keyboard', 'Accessories', 129.99, 'RGB backlit mechanical keyboard', 'In Stock'],
        ['Monitor Stand', 'Accessories', 79.99, 'Adjustable dual monitor stand', 'Out of Stock']
    ]

    # Add headers with formatting
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, color="FFFFFF", size=12)
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )

    # Add data with formatting
    for row, row_data in enumerate(data, 2):
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=row, column=col, value=value)

            # Alternate row colors
            if row % 2 == 0:
                cell.fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")

            # Special formatting for price column
            if col == 3:  # Price column
                cell.number_format = '$#,##0.00'
                cell.font = Font(bold=True, color="008000")

            # Special formatting for status column
            if col == 5:  # Status column
                if value == "In Stock":
                    cell.font = Font(color="008000", bold=True)
                elif value == "Low Stock":
                    cell.font = Font(color="FF8C00", bold=True)
                elif value == "Out of Stock":
                    cell.font = Font(color="FF0000", bold=True)

            cell.border = Border(
                left=Side(style="thin"),
                right=Side(style="thin"),
                top=Side(style="thin"),
                bottom=Side(style="thin")
            )
            cell.alignment = Alignment(horizontal="left", vertical="center")

    # Auto-adjust column widths
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 20

    # Add a second sheet with different formatting
    ws2 = wb.create_sheet("Sales Data")
    ws2['A1'] = "Monthly Sales Report"
    ws2['A1'].font = Font(size=16, bold=True, color="000080")
    ws2['A1'].alignment = Alignment(horizontal="center")

    # Merge cells for title
    ws2.merge_cells('A1:D1')

    wb.save('formatted_sample.xlsx')
    print("Formatted sample Excel file 'formatted_sample.xlsx' created with rich formatting!")

# Uncomment the line below to create a sample file for testing
# create_sample_excel()