# Excel Translation Tool

This tool translates Excel files from English to Japanese, including headers and all text content.

## Files

- `demo.py` - Main translation script
- `run_translation.sh` - Easy-to-use script that handles virtual environment
- `demo.xlsx` - Original Excel file (input)
- `demo_gen.xlsx` - Translated Excel file (output)
- `demo_backup.xlsx` - Backup of original file

## How to Use

### Option 1: Use the convenience script (RECOMMENDED)
```bash
./run_translation.sh
```

### Option 2: Manual execution
```bash
bash -c "source venv/bin/activate && python demo.py"
```

### Option 3: Step by step
```bash
source venv/bin/activate
python demo.py
```

## What it does

✅ **Translates headers**: `Title -> タイトル`, `Result -> 結果`  
✅ **Translates all data**: English text -> Japanese text  
✅ **Creates backup**: Original file is safely backed up  
✅ **Removes table definitions**: Prevents XML corruption errors  
✅ **Clean output**: No Excel recovery warnings  

## Requirements

- Python 3.x
- Virtual environment with `openpyxl` and `deep-translator` packages
- Internet connection (for Google Translate API)

## Troubleshooting

If you get `ModuleNotFoundError: No module named 'openpyxl'`:
- Make sure to use the convenience script `./run_translation.sh`
- Or manually activate the virtual environment first: `source venv/bin/activate`

## Output

- **Input**: `demo.xlsx` (English)
- **Output**: `demo_gen.xlsx` (Japanese)
- **Backup**: `demo_backup.xlsx` (Original copy)
