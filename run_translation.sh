#!/bin/bash

# Excel Translation Script
# Translates Excel files from English to Japanese including headers

echo "🔄 Starting Excel Translation..."
echo "📁 Current directory: $(pwd)"

# Check if virtual environment exists
if [ ! -f "venv/bin/activate" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please create it first:"
    echo "   python3 -m venv venv"
    echo "   source venv/bin/activate"
    echo "   pip install openpyxl deep-translator"
    exit 1
fi

# Run translation with virtual environment
echo "✅ Running translation with virtual environment..."
bash -c "source venv/bin/activate && python demo.py"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Translation completed successfully!"
    echo "📄 Check demo_gen.xlsx for the translated file"
    echo "💾 Original file backed up as demo_backup.xlsx"
else
    echo "❌ Translation failed. Please check the error messages above."
    exit 1
fi
