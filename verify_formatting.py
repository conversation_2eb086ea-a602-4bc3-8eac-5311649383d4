#!/usr/bin/env python3
"""
Verification script to demonstrate formatting preservation
"""

from openpyxl import load_workbook
from demo import ExcelTranslator
import os

def compare_formatting(original_file, formatted_file, no_format_file):
    """
    Compare formatting between original, formatted translation, and no-format translation
    """
    print("=" * 80)
    print("🔍 FORMATTING PRESERVATION VERIFICATION")
    print("=" * 80)
    
    # Load workbooks
    original_wb = load_workbook(original_file)
    formatted_wb = load_workbook(formatted_file)
    
    print(f"\n📁 Original file: {original_file}")
    print(f"📁 Formatted translation: {formatted_file}")
    if os.path.exists(no_format_file):
        print(f"📁 No-format translation: {no_format_file}")
        no_format_wb = load_workbook(no_format_file)
    else:
        print(f"📁 No-format translation: {no_format_file} (not found)")
        no_format_wb = None
    
    # Check first sheet
    sheet_name = original_wb.sheetnames[0]
    original_ws = original_wb[sheet_name]
    formatted_ws = formatted_wb[sheet_name]
    
    print(f"\n📋 Analyzing sheet: '{sheet_name}'")
    print("-" * 50)
    
    # Check a few key cells for formatting
    test_cells = ['A1', 'B1', 'A2', 'B2']
    
    for cell_addr in test_cells:
        orig_cell = original_ws[cell_addr]
        fmt_cell = formatted_ws[cell_addr]
        
        print(f"\n🔸 Cell {cell_addr}:")
        print(f"   Original value: '{orig_cell.value}'")
        print(f"   Translated value: '{fmt_cell.value}'")
        
        # Check formatting preservation
        formatting_preserved = True
        
        # Font
        if orig_cell.font.bold != fmt_cell.font.bold:
            formatting_preserved = False
            print(f"   ❌ Font bold: {orig_cell.font.bold} → {fmt_cell.font.bold}")
        else:
            print(f"   ✅ Font bold preserved: {fmt_cell.font.bold}")
        
        # Fill color
        if orig_cell.fill.start_color.rgb != fmt_cell.fill.start_color.rgb:
            formatting_preserved = False
            print(f"   ❌ Fill color: {orig_cell.fill.start_color.rgb} → {fmt_cell.fill.start_color.rgb}")
        else:
            print(f"   ✅ Fill color preserved: {fmt_cell.fill.start_color.rgb}")
        
        # Font color
        if orig_cell.font.color and fmt_cell.font.color:
            if orig_cell.font.color.rgb != fmt_cell.font.color.rgb:
                formatting_preserved = False
                print(f"   ❌ Font color: {orig_cell.font.color.rgb} → {fmt_cell.font.color.rgb}")
            else:
                print(f"   ✅ Font color preserved: {fmt_cell.font.color.rgb}")
        
        if formatting_preserved:
            print(f"   🎉 All formatting preserved for cell {cell_addr}!")

def create_comparison_demo():
    """
    Create a demonstration comparing old vs new translation methods
    """
    print("\n" + "=" * 80)
    print("🚀 CREATING COMPARISON DEMO")
    print("=" * 80)
    
    translator = ExcelTranslator(source_lang='en', target_lang='ja', delay=0.1)
    
    # Create files for comparison
    input_file = "formatted_sample.xlsx"
    formatted_output = "demo_with_formatting.xlsx"
    no_format_output = "demo_without_formatting.xlsx"
    
    if os.path.exists(input_file):
        print(f"\n📝 Translating {input_file} with TWO different methods...")
        
        # Method 1: With formatting preservation (NEW)
        print("\n🎨 Method 1: WITH formatting preservation...")
        translator.translate_excel_with_formatting(
            input_file, formatted_output, translate_headers=True
        )
        print(f"✅ Saved to: {formatted_output}")
        
        # Method 2: Without formatting preservation (OLD)
        print("\n📄 Method 2: WITHOUT formatting preservation (old method)...")
        translator.translate_excel_file(
            input_file, no_format_output, translate_headers=True
        )
        print(f"✅ Saved to: {no_format_output}")
        
        print(f"\n🔍 Now comparing the results...")
        compare_formatting(input_file, formatted_output, no_format_output)
        
        print(f"\n" + "=" * 80)
        print("📊 SUMMARY")
        print("=" * 80)
        print(f"✅ {formatted_output} - Preserves ALL formatting, colors, fonts, styles")
        print(f"❌ {no_format_output} - Loses ALL formatting (plain text only)")
        print(f"\n💡 Open both files in Excel to see the dramatic difference!")
        
    else:
        print(f"❌ Input file {input_file} not found!")

if __name__ == "__main__":
    create_comparison_demo()
