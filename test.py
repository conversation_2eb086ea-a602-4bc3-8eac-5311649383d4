from openpyxl import load_workbook

print("=== VERIFYING HEADER TRANSLATION ===")

# Load both files
original_wb = load_workbook('/Users/<USER>/workspace/my-docs/project/demo.xlsx')
translated_wb = load_workbook('/Users/<USER>/workspace/my-docs/project/demo_gen.xlsx')

for sheet_name in original_wb.sheetnames:
    print(f"\n--- Sheet: {sheet_name} ---")

    original_sheet = original_wb[sheet_name]
    translated_sheet = translated_wb[sheet_name]

    print("Comparing all cells (including headers):")
    for row in range(1, 7):  # Check first 6 rows
        for col in range(1, 3):  # Check first 2 columns
            orig_cell = original_sheet.cell(row=row, column=col)
            trans_cell = translated_sheet.cell(row=row, column=col)

            if orig_cell.value:
                row_type = "HEADER" if row == 1 else "DATA"
                print(f"  {orig_cell.coordinate} ({row_type}): '{orig_cell.value}' -> '{trans_cell.value}'")

                # Check if translation was applied
                if orig_cell.value == trans_cell.value:
                    print(f"    ⚠️  WARNING: No translation applied")
                else:
                    print(f"    ✅ Translated successfully")

print("\n=== END VERIFICATION ===")